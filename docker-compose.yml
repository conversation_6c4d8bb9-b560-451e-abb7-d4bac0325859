services:
  clickhouse:
    image: clickhouse/clickhouse-server:23.8
    container_name: clickhouse-server
    hostname: clickhouse
    ports:
      - "8123:8123"  # HTTP interface
      - "9000:9000"  # Native interface
    volumes:
      - clickhouse_data:/var/lib/clickhouse
      - ./clickhouse/config:/etc/clickhouse-server/config.d:ro
      - /home/<USER>/projects/indiebi/data-jobs/jobs/core_silver/playground/delta:/data/delta:ro
    environment:
      CLICKHOUSE_DB: analytics
      CLICKHOUSE_DEFAULT_ACCESS_MANAGEMENT: 1
      CLICKHOUSE_DO_NOT_CHOWN: 1  # Prevent ownership changes on mounted volumes
    ulimits:
      nofile:
        soft: 262144
        hard: 262144
    networks:
      - analytics-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8123/ping"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  superset:
    image: apache/superset:3.0.0
    container_name: superset
    hostname: superset
    ports:
      - "8088:8088"
    volumes:
      - superset_data:/app/superset_home
      - ./superset/config:/app/pythonpath
      - ./superset/init-superset.py:/app/init-superset.py
    environment:
      - SUPERSET_CONFIG_PATH=/app/pythonpath/superset_config.py
      - SUPERSET_SECRET_KEY=your-secret-key-change-this-in-production
    depends_on:
      clickhouse:
        condition: service_healthy
    networks:
      - analytics-network
    command: >
      bash -c "
      pip install clickhouse-driver clickhouse-connect 'clickhouse-sqlalchemy==0.2.4' &&
      superset db upgrade &&
      superset fab create-admin --username admin --firstname Admin --lastname User --email <EMAIL> --password admin &&
      superset init &&
      superset run -h 0.0.0.0 -p 8088 --with-threads --reload --debugger
      "

  superset-init:
    image: apache/superset:3.0.0
    container_name: superset-init
    volumes:
      - superset_data:/app/superset_home
      - ./superset/config:/app/pythonpath
    environment:
      - SUPERSET_CONFIG_PATH=/app/pythonpath/superset_config.py
      - SUPERSET_SECRET_KEY=your-secret-key-change-this-in-production
    depends_on:
      clickhouse:
        condition: service_healthy
    networks:
      - analytics-network
    command: >
      bash -c "
      pip install clickhouse-connect &&
      echo 'ClickHouse driver installed successfully'
      "
    profiles:
      - init

volumes:
  clickhouse_data:
  superset_data:

networks:
  analytics-network:
    driver: bridge
