#!/bin/bash

# Analytics Stack Startup Script
# This script starts ClickHouse and Apache Superset with Delta Lake integration

set -e

echo "🚀 Starting Analytics Stack..."
echo "================================"

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Check if docker-compose.yml exists
if [ ! -f "docker-compose.yml" ]; then
    echo "❌ docker-compose.yml not found. Please run this script from the project root."
    exit 1
fi

# Create necessary directories if they don't exist
echo "📁 Creating necessary directories..."
mkdir -p clickhouse/config clickhouse/users superset/config

# Pull latest images
echo "📥 Pulling latest Docker images..."
docker-compose pull

# Start the services
echo "🔧 Starting ClickHouse and Superset services..."
docker-compose up -d clickhouse

# Wait for ClickHouse to be ready
echo "⏳ Waiting for ClickHouse to be ready..."
timeout=120
counter=0
while ! docker exec clickhouse-server clickhouse-client --query "SELECT 1" > /dev/null 2>&1; do
    if [ $counter -ge $timeout ]; then
        echo "❌ ClickHouse failed to start within $timeout seconds"
        echo "📋 ClickHouse logs:"
        docker-compose logs clickhouse
        echo ""
        echo "🔧 Trying to restart ClickHouse..."
        docker-compose restart clickhouse
        sleep 10
        if docker exec clickhouse-server clickhouse-client --query "SELECT 1" > /dev/null 2>&1; then
            echo "✅ ClickHouse restarted successfully!"
            break
        else
            echo "❌ ClickHouse restart failed"
            exit 1
        fi
    fi
    echo "   Waiting for ClickHouse... ($counter/$timeout)"
    sleep 3
    counter=$((counter + 3))
done

echo "✅ ClickHouse is ready!"

# Initialize ClickHouse tables and users
echo "🗄️  Initializing ClickHouse tables and users..."
if [ -f "clickhouse/init-tables.sql" ]; then
    docker exec -i clickhouse-server clickhouse-client --multiquery < clickhouse/init-tables.sql
    echo "✅ ClickHouse tables and users initialized!"
else
    echo "⚠️  Warning: clickhouse/init-tables.sql not found. Tables not initialized."
fi

# Install ClickHouse driver for Superset
echo "🔌 Installing ClickHouse driver for Superset..."
docker-compose run --rm superset-init

# Start Superset
echo "🎨 Starting Superset..."
docker-compose up -d superset

# Wait for Superset to be ready
echo "⏳ Waiting for Superset to be ready..."
timeout=120
counter=0
while ! curl -s http://localhost:8088/health > /dev/null 2>&1; do
    if [ $counter -ge $timeout ]; then
        echo "❌ Superset failed to start within $timeout seconds"
        docker-compose logs superset
        exit 1
    fi
    echo "   Waiting for Superset... ($counter/$timeout)"
    sleep 5
    counter=$((counter + 5))
done

echo ""
echo "🎉 Analytics Stack is ready!"
echo "================================"
echo "📊 ClickHouse HTTP Interface: http://localhost:8123"
echo "🎨 Apache Superset: http://localhost:8088"
echo ""
echo "Default Credentials:"
echo "  ClickHouse: admin / admin123"
echo "  Superset: admin / admin"
echo ""
echo "📋 To connect Superset to ClickHouse:"
echo "  1. Go to http://localhost:8088"
echo "  2. Login with admin/admin"
echo "  3. Go to Settings > Database Connections"
echo "  4. Add new database with:"
echo "     - Database: ClickHouse"
echo "     - SQLAlchemy URI: clickhouse+native://admin:admin123@clickhouse:9000/analytics"
echo ""
echo "🛑 To stop the stack, run: ./stop-analytics-stack.sh"
echo ""
