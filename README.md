# Analytics Stack with <PERSON>lickHouse, <PERSON>set, and Delta Lake

This project sets up a complete data analytics stack that connects ClickHouse database to Delta Lake tables and provides a business intelligence interface through Apache Superset.

## 🏗️ Architecture

- **ClickHouse**: High-performance columnar database server
- **Apache Superset**: Modern business intelligence web application
- **Delta Lake Integration**: Direct access to Delta Lake tables via ClickHouse File engine
- **Docker Compose**: Containerized deployment for easy management

## 📋 Prerequisites

1. **Docker Desktop** with WSL2 integration enabled
   - Install Docker Desktop for Windows
   - Enable WSL2 integration in Docker Desktop settings
   - Ensure Docker is accessible from your WSL2 environment

2. **Delta Lake Data**: Your Delta Lake tables should be located at:
   ```
   /home/<USER>/projects/indiebi/data-jobs/jobs/core_silver/playground/delta/
   ```

## 🚀 Quick Start

### 1. Start the Analytics Stack

```bash
./start-analytics-stack.sh
```

This script will:
- Pull the latest Docker images
- Start ClickHouse server
- Initialize ClickHouse tables mapped to your Delta Lake data
- Install ClickHouse driver for Superset
- Start Apache Superset
- Provide connection details

### 2. Access the Services

- **<PERSON>lickHouse HTTP Interface**: http://localhost:8123
- **Apache Superset**: http://localhost:8088

### 3. Default Credentials

- **ClickHouse**: `admin` / `admin123`
- **Superset**: `admin` / `admin`

### 4. Connect Superset to ClickHouse

1. Open Superset at http://localhost:8088
2. Login with `admin` / `admin`
3. Go to **Settings** → **Database Connections**
4. Click **+ Database**
5. Select **ClickHouse** as the database type
6. Use this connection string:
   ```
   clickhouse+native://admin:admin123@clickhouse:9000/analytics
   ```
7. Test the connection and save

## 📊 Available Tables

Your Delta Lake tables are automatically mapped to ClickHouse tables:

- `dim_portals` - Portal dimension data
- `dim_sku` - SKU dimension data
- `fact_baseline` - Baseline metrics
- `fact_discounts` - Discount transactions
- `fact_sales` - Sales transactions
- `observation_discounts` - Discount observations
- `observation_sales` - Sales observations
- `silver_acquisition_properties` - Acquisition properties
- `silver_portals` - Portal metadata
- `silver_reports` - Report metadata
- `silver_skus` - SKU metadata

## 🛑 Stop the Stack

```bash
./stop-analytics-stack.sh
```

## 🔧 Configuration Files

### Docker Compose
- `docker-compose.yml` - Main orchestration file

### ClickHouse Configuration
- `clickhouse/config/delta_lake.xml` - ClickHouse server configuration
- `clickhouse/users/admin.xml` - User authentication and permissions
- `clickhouse/init-tables.sql` - Table definitions for Delta Lake data

### Superset Configuration
- `superset/config/superset_config.py` - Superset application configuration

## 🐛 Troubleshooting

### Docker Issues
If you get "Docker is not running" error:
1. Ensure Docker Desktop is running
2. Enable WSL2 integration in Docker Desktop settings
3. Restart your WSL2 terminal

### ClickHouse Connection Issues
- Check if ClickHouse is running: `docker ps`
- View ClickHouse logs: `docker-compose logs clickhouse`
- Test connection: `curl http://localhost:8123/ping`

### Superset Connection Issues
- Ensure ClickHouse driver is installed
- Check Superset logs: `docker-compose logs superset`
- Verify connection string format

### Data Access Issues
- Ensure Delta Lake path is correctly mounted
- Check file permissions on Delta Lake directory
- Verify Parquet files exist in expected locations

## 📁 Project Structure

```
offgridbi/
├── docker-compose.yml
├── start-analytics-stack.sh
├── stop-analytics-stack.sh
├── clickhouse/
│   ├── config/
│   │   └── delta_lake.xml
│   ├── users/
│   │   └── admin.xml
│   └── init-tables.sql
└── superset/
    └── config/
        └── superset_config.py
```

## 🔄 Data Updates

When your Delta Lake data is updated:
1. ClickHouse will automatically read the latest data
2. No restart required for data updates
3. Superset dashboards will reflect new data on refresh

## 🎯 Next Steps

1. Create dashboards in Superset using your Delta Lake data
2. Set up automated data refresh schedules
3. Configure additional data sources if needed
4. Customize Superset themes and permissions

## ✅ **SETUP COMPLETE!**

Your analytics stack is now running successfully:

### 🌐 **Access Your Services:**
- **ClickHouse Web Interface**: http://localhost:8123
- **Superset Dashboard**: http://localhost:8088 (admin/admin)

### 📊 **Available Data:**
- **dim_portals**: 19 rows (platform, region, store data)
- **dim_sku**: 649 rows (product and SKU information)

### 🔗 **Connect Superset to ClickHouse:**
1. Login to Superset at http://localhost:8088 (admin/admin)
2. Go to **Settings** → **Database Connections**
3. Click **+ Database**
4. Select **ClickHouse** as the database type
5. **IMPORTANT**: Use the HTTP connection (not native):
   ```
   clickhouse+http://clickhouse:8123/analytics
   ```

### ⚠️ **Connection Troubleshooting:**

**If you get "response code 400" error:**
- **Protocol**: HTTP (not native)
- **Port**: 8123 (not 9000)
- **Full URI**: `clickhouse+http://clickhouse:8123/analytics`

**If you can't see schemas/databases:**
1. Delete the current connection and recreate it
2. Use this connection string: `clickhouse+*******************************************/`
3. After connecting, click "Force refresh schema list"
4. You should see `analytics` and `default` as available schemas
5. Select `analytics` schema to access your Delta Lake tables

**If you see generic column names (col1, col2, col3) or File engine errors:**
- Run `./create-delta-lake-tables.sh` to recreate tables with proper Delta Lake integration
- Tables now read ALL Parquet files using `delta/table_name/**/*.parquet` pattern
- The `fact_sales` table has 26 real columns: `gross_sales`, `units_sold`, `date`, `portal_platform_region`, etc.
- No more "Format Parquet not supported" errors

## 📞 Support

For issues or questions:
1. Check the troubleshooting section above
2. Review Docker and service logs
3. Ensure all prerequisites are met