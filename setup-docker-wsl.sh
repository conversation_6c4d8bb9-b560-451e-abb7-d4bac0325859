#!/bin/bash

# Docker Setup Verification for WSL2
# This script helps verify and troubleshoot Docker setup in WSL2

echo "🐳 Docker Setup Verification for WSL2"
echo "====================================="

# Check if we're in WSL
if grep -qi microsoft /proc/version; then
    echo "✅ Running in WSL environment"
else
    echo "⚠️  Not running in WSL - this script is designed for WSL2"
fi

echo ""
echo "🔍 Checking Docker installation..."

# Check if docker command exists
if command -v docker &> /dev/null; then
    echo "✅ Docker command found at: $(which docker)"
else
    echo "❌ Docker command not found"
    echo ""
    echo "📋 To fix this issue:"
    echo "1. Install Docker Desktop for Windows"
    echo "2. Enable WSL2 integration in Docker Desktop settings:"
    echo "   - Open Docker Desktop"
    echo "   - Go to Settings → Resources → WSL Integration"
    echo "   - Enable integration with your WSL2 distro"
    echo "3. Restart your WSL2 terminal"
    exit 1
fi

# Check if docker-compose command exists
if command -v docker-compose &> /dev/null; then
    echo "✅ Docker Compose found at: $(which docker-compose)"
else
    echo "❌ Docker Compose not found"
    echo "   This should be included with Docker Desktop"
fi

echo ""
echo "🔍 Testing Docker connectivity..."

# Test Docker daemon connection
if docker info &> /dev/null; then
    echo "✅ Docker daemon is accessible"
    
    # Show Docker version info
    echo "📊 Docker version information:"
    docker version --format "   Client: {{.Client.Version}}"
    docker version --format "   Server: {{.Server.Version}}"
    
else
    echo "❌ Cannot connect to Docker daemon"
    echo ""
    echo "📋 Troubleshooting steps:"
    echo "1. Ensure Docker Desktop is running on Windows"
    echo "2. Check WSL2 integration settings in Docker Desktop:"
    echo "   - Settings → Resources → WSL Integration"
    echo "   - Enable integration with your WSL2 distro"
    echo "3. Restart Docker Desktop"
    echo "4. Restart your WSL2 terminal"
    echo "5. Try running: wsl --shutdown (from Windows) then restart WSL"
    exit 1
fi

echo ""
echo "🔍 Testing Docker functionality..."

# Test basic Docker functionality
if docker run --rm hello-world &> /dev/null; then
    echo "✅ Docker can run containers successfully"
else
    echo "❌ Docker cannot run containers"
    echo "   Check Docker Desktop status and WSL2 integration"
    exit 1
fi

echo ""
echo "🔍 Checking file system access..."

# Check if the Delta Lake path exists and is accessible
DELTA_PATH="/home/<USER>/projects/indiebi/data-jobs/jobs/core_silver/playground/delta"
if [ -d "$DELTA_PATH" ]; then
    echo "✅ Delta Lake directory exists: $DELTA_PATH"
    
    # Count parquet files
    parquet_count=$(find "$DELTA_PATH" -name "*.parquet" 2>/dev/null | wc -l)
    echo "📄 Found $parquet_count Parquet files in Delta Lake directory"
    
    # List table directories
    echo "📁 Available table directories:"
    ls -1 "$DELTA_PATH" | sed 's/^/   - /'
    
else
    echo "❌ Delta Lake directory not found: $DELTA_PATH"
    echo "   Please verify the path to your Delta Lake data"
fi

echo ""
echo "🎯 Next Steps:"
echo "==============="

if docker info &> /dev/null; then
    echo "✅ Docker is ready! You can now run:"
    echo "   ./start-analytics-stack.sh"
    echo ""
    echo "📋 After starting the stack:"
    echo "   - ClickHouse: http://localhost:8123"
    echo "   - Superset: http://localhost:8088"
    echo "   - Run ./test-setup.sh to verify everything works"
else
    echo "❌ Docker needs to be configured first"
    echo "   Follow the troubleshooting steps above"
fi

echo ""
