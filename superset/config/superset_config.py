import os
from datetime import timedelta

# Superset specific config
ROW_LIMIT = 5000
SUPERSET_WEBSERVER_PORT = 8088

# Flask App Builder configuration
# Your App secret key
SECRET_KEY = os.environ.get('SUPERSET_SECRET_KEY', 'your-secret-key-change-this-in-production')

# The SQLAlchemy connection string to your database backend
# This connection defines the path to the database that stores your
# superset metadata (slices, connections, tables, dashboards, ...)
# Note that the connection information to connect to the datasources
# you want to explore are managed directly in the web UI
SQLALCHEMY_DATABASE_URI = 'sqlite:////app/superset_home/superset.db'

# Flask-WTF flag for CSRF
WTF_CSRF_ENABLED = True
# Add endpoints that need to be exempt from CSRF protection
WTF_CSRF_EXEMPT_LIST = []

# Set this API key to enable Mapbox visualizations
MAPBOX_API_KEY = ''

# Cache configuration
CACHE_CONFIG = {
    'CACHE_TYPE': 'simple',
}

# Enable feature flags
FEATURE_FLAGS = {
    'ENABLE_TEMPLATE_PROCESSING': True,
    'DASHBOARD_NATIVE_FILTERS': True,
    'DASHBOARD_CROSS_FILTERS': True,
    'DASHBOARD_RBAC': True,
    'ENABLE_EXPLORE_JSON_CSRF_PROTECTION': False,
    'ENABLE_EXPLORE_DRAG_AND_DROP': True,
    'GLOBAL_ASYNC_QUERIES': True,
}

# Async query configuration
REDIS_HOST = "localhost"
REDIS_PORT = 6379

class CeleryConfig(object):
    BROKER_URL = f"redis://{REDIS_HOST}:{REDIS_PORT}/0"
    CELERY_IMPORTS = (
        'superset.sql_lab',
        'superset.tasks',
    )
    CELERY_RESULT_BACKEND = f"redis://{REDIS_HOST}:{REDIS_PORT}/0"
    CELERYD_LOG_LEVEL = 'DEBUG'
    CELERYD_PREFETCH_MULTIPLIER = 10
    CELERY_ACKS_LATE = True
    CELERY_ANNOTATIONS = {
        'sql_lab.get_sql_results': {
            'rate_limit': '100/s',
        },
        'email_reports.send': {
            'rate_limit': '1/s',
            'time_limit': 120,
            'soft_time_limit': 150,
            'ignore_result': True,
        },
    }
    CELERYBEAT_SCHEDULE = {
        'email_reports.schedule_hourly': {
            'task': 'email_reports.schedule_hourly',
            'schedule': timedelta(hours=1),
            'options': {
                'expires': 1 * 60,
            },
        },
    }

CELERY_CONFIG = CeleryConfig

# SQL Lab configuration
SQLLAB_CTAS_NO_LIMIT = True

# Security configuration
SECRET_KEY = 'your-secret-key-here-change-this-in-production-at-least-32-bytes-long-for-superset'
JWT_SECRET_KEY = 'your-jwt-secret-key-here-change-this-in-production-at-least-32-bytes-long-for-superset'
TALISMAN_ENABLED = False
ENABLE_PROXY_FIX = True

# Async query configuration
FEATURE_FLAGS = {
    "ENABLE_TEMPLATE_PROCESSING": True,
}

# Disable async queries to avoid JWT issues for now
GLOBAL_ASYNC_QUERIES_TRANSPORT = None
GLOBAL_ASYNC_QUERIES_JWT_COOKIE_NAME = None
GLOBAL_ASYNC_QUERIES_JWT_COOKIE_SECURE = False
GLOBAL_ASYNC_QUERIES_JWT_COOKIE_DOMAIN = None
GLOBAL_ASYNC_QUERIES_JWT_SECRET = 'your-jwt-secret-key-here-change-this-in-production-at-least-32-bytes-long-for-superset'

# Pre-configured databases
DATABASES = {
    'ClickHouse Analytics': {
        'sqlalchemy_uri': 'clickhouse+http://clickhouse:8123/analytics',
        'expose_in_sqllab': True,
        'allow_ctas': True,
        'allow_cvas': True,
        'allow_dml': True,
        'allow_run_async': True,
        'cache_timeout': None,
        'extra': '{"metadata_params": {}, "engine_params": {}, "metadata_cache_timeout": {}, "schemas_allowed_for_csv_upload": []}'
    }
}

# Email configuration (optional)
SMTP_HOST = "localhost"
SMTP_STARTTLS = True
SMTP_SSL = False
SMTP_USER = "superset"
SMTP_PORT = 25
SMTP_PASSWORD = "superset"
SMTP_MAIL_FROM = "<EMAIL>"

# WebDriver configuration for reports
WEBDRIVER_BASEURL = "http://superset:8088/"
WEBDRIVER_BASEURL_USER_FRIENDLY = WEBDRIVER_BASEURL

# Custom CSS
CUSTOM_CSS = """
.navbar-brand {
    font-weight: bold;
}
"""

# Database connection timeout (SQLite doesn't support pool_timeout)
# SQLALCHEMY_ENGINE_OPTIONS = {
#     'pool_timeout': 20,
#     'pool_recycle': -1
# }

# Allow embedding of Superset in iframes
HTTP_HEADERS = {'X-Frame-Options': 'ALLOWALL'}

# Logging configuration
ENABLE_TIME_ROTATE = False
TIME_ROTATE_LOG_LEVEL = 'INFO'
# FILENAME = os.path.join(os.path.expanduser('~'), '.superset', 'superset.log')

# Dashboard and chart configuration
DASHBOARD_AUTO_REFRESH_MODE = "fetch"
DASHBOARD_AUTO_REFRESH_INTERVALS = [
    [0, "Don't refresh"],
    [10, "10 seconds"],
    [30, "30 seconds"],
    [60, "1 minute"],
    [300, "5 minutes"],
    [1800, "30 minutes"],
    [3600, "1 hour"],
]

# SQL Lab result backend
RESULTS_BACKEND = None

# Allowed file extensions for CSV upload
ALLOWED_EXTENSIONS = {'csv', 'tsv', 'txt'}

# Maximum file size for uploads (in bytes)
MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB

# Enable SQL Lab
ENABLE_JAVASCRIPT_CONTROLS = True
