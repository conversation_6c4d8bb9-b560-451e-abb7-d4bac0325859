#!/usr/bin/env python3
"""
Superset initialization script to automatically configure ClickHouse database connection
"""

import os
import sys

# Add the superset path
sys.path.insert(0, '/app')

def init_superset():
    """Initialize Superset with ClickHouse database connection"""

    print("🚀 Initializing Superset with ClickHouse connection...")

    try:
        # Import after setting up the path
        from superset import app, db
        from superset.models.core import Database

        # Create application context
        with app.app_context():
            # Check if ClickHouse database already exists
            existing_db = db.session.query(Database).filter_by(database_name='ClickHouse Analytics').first()

            if existing_db:
                print("✅ ClickHouse database connection already exists")
                return

            # Create ClickHouse database connection
            clickhouse_db = Database(
                database_name='ClickHouse Analytics',
                sqlalchemy_uri='clickhouse+http://clickhouse:8123/analytics',
                expose_in_sqllab=True,
                allow_ctas=True,
                allow_cvas=True,
                allow_dml=True,
                allow_run_async=True,
                cache_timeout=None,
                extra='{"metadata_params": {}, "engine_params": {}, "metadata_cache_timeout": {}, "schemas_allowed_for_csv_upload": []}'
            )

            try:
                # Add to session and commit
                db.session.add(clickhouse_db)
                db.session.commit()
                print("✅ ClickHouse database connection created successfully!")
                print("   Database Name: ClickHouse Analytics")
                print("   Connection URI: clickhouse+http://clickhouse:8123/analytics")

            except Exception as e:
                print(f"❌ Error creating ClickHouse database connection: {e}")
                db.session.rollback()

            finally:
                db.session.close()

    except Exception as e:
        print(f"❌ Error initializing Superset: {e}")
        print("   This is normal if Superset is not fully initialized yet")

if __name__ == '__main__':
    init_superset()
