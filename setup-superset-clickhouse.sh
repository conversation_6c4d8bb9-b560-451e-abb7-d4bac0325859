#!/bin/bash

echo "🔗 Setting up ClickHouse connection in Superset..."
echo "================================================"

# Wait for Superset to be ready
echo "⏳ Waiting for Superset to be ready..."
timeout=300
counter=0

while [ $counter -lt $timeout ]; do
    if curl -s http://localhost:8088/health > /dev/null 2>&1; then
        echo "✅ Superset is ready!"
        break
    fi
    sleep 5
    counter=$((counter + 5))
    echo "   Still waiting... ($counter/$timeout seconds)"
done

if [ $counter -ge $timeout ]; then
    echo "❌ Timeout waiting for Superset to be ready"
    exit 1
fi

# Try to run the initialization script
echo "🚀 Running ClickHouse database setup..."
docker exec superset python /app/init-superset.py

echo ""
echo "✅ Setup complete!"
echo ""
echo "📋 Manual Setup Instructions (if automatic setup failed):"
echo "1. Go to http://localhost:8088"
echo "2. Login with admin/admin"
echo "3. Go to Settings → Database Connections"
echo "4. Click '+ Database'"
echo "5. Select 'ClickHouse' as database type"
echo "6. Use this connection string:"
echo "   clickhouse+http://clickhouse:8123/analytics"
echo "7. Test connection and save"
echo ""
echo "🎯 Correct Connection Details:"
echo "   Database Type: ClickHouse"
echo "   Host: clickhouse"
echo "   Port: 8123"
echo "   Database: analytics"
echo "   Protocol: HTTP (not native)"
echo "   Full URI: clickhouse+http://clickhouse:8123/analytics"
