#!/bin/bash

# Analytics Stack Shutdown Script
# This script stops ClickHouse and Apache Superset services

set -e

echo "🛑 Stopping Analytics Stack..."
echo "==============================="

# Check if docker-compose.yml exists
if [ ! -f "docker-compose.yml" ]; then
    echo "❌ docker-compose.yml not found. Please run this script from the project root."
    exit 1
fi

# Stop all services
echo "🔧 Stopping all services..."
docker-compose down

# Optional: Remove volumes (uncomment if you want to reset all data)
# echo "🗑️  Removing volumes..."
# docker-compose down -v

echo ""
echo "✅ Analytics Stack stopped successfully!"
echo ""
echo "📝 Note: Data volumes are preserved."
echo "   To completely reset and remove all data, run:"
echo "   docker-compose down -v"
echo ""
