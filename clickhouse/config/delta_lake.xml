<?xml version="1.0"?>
<clickhouse>
    <!-- Delta Lake data is accessible via File engine at /data/delta/ -->

    <!-- Enable necessary table engines -->
    <table_engines>
        <DeltaLake>1</DeltaLake>
        <S3>1</S3>
        <URL>1</URL>
        <File>1</File>
    </table_engines>

    <!-- Enable HTTP interface for external connections -->
    <http_port>8123</http_port>
    <tcp_port>9000</tcp_port>

    <!-- Disable interserver communication (not needed for single node) -->
    <interserver_http_port remove="remove"/>

    <!-- Allow connections from any host (IPv4 only) -->
    <listen_host>0.0.0.0</listen_host>

    <!-- Logging configuration -->
    <logger>
        <level>information</level>
        <console>true</console>
    </logger>

    <!-- Server-level performance settings -->
    <max_concurrent_queries>100</max_concurrent_queries>

    <!-- Enable query log -->
    <query_log>
        <database>system</database>
        <table>query_log</table>
        <flush_interval_milliseconds>7500</flush_interval_milliseconds>
    </query_log>

    <!-- Cache settings -->
    <disable_internal_dns_cache>1</disable_internal_dns_cache>
    <mark_cache_size>5368709120</mark_cache_size>
    <uncompressed_cache_size>8589934592</uncompressed_cache_size>

    <!-- Skip check for incorrect settings to avoid user-level setting errors -->
    <skip_check_for_incorrect_settings>1</skip_check_for_incorrect_settings>
</clickhouse>
