-- Create analytics database
CREATE DATABASE IF NOT EXISTS analytics;

-- Use analytics database
USE analytics;

-- Create dim_portals table
CREATE TABLE IF NOT EXISTS dim_portals
(
    studio_id UInt32,
    portal String,
    -- Add other columns as needed based on your data structure
    portal_id Nullable(String),
    portal_name Nullable(String),
    created_at Nullable(DateTime),
    updated_at Nullable(DateTime)
)
ENGINE = File(Parquet, '/data/delta/dim_portals/*/*.parquet');

-- Create dim_sku table
CREATE TABLE IF NOT EXISTS dim_sku
(
    studio_id UInt32,
    sku_id Nullable(String),
    sku_name Nullable(String),
    product_type Nullable(String),
    price Nullable(Float64),
    created_at Nullable(DateTime),
    updated_at Nullable(DateTime)
)
ENGINE = File(Parquet, '/data/delta/dim_sku/*/*.parquet');

-- Create fact_baseline table
CREATE TABLE IF NOT EXISTS fact_baseline
(
    studio_id UInt32,
    date Date,
    portal Nullable(String),
    sku_id Nullable(String),
    baseline_revenue Nullable(Float64),
    baseline_units Nullable(UInt32),
    created_at Nullable(DateTime)
)
ENGINE = File(Parquet, '/data/delta/fact_baseline/*/*.parquet');

-- Create fact_discounts table
CREATE TABLE IF NOT EXISTS fact_discounts
(
    studio_id UInt32,
    date Date,
    portal Nullable(String),
    sku_id Nullable(String),
    discount_percentage Nullable(Float64),
    discount_revenue Nullable(Float64),
    discount_units Nullable(UInt32),
    created_at Nullable(DateTime)
)
ENGINE = File(Parquet, '/data/delta/fact_discounts/*/*.parquet');

-- Create fact_sales table
CREATE TABLE IF NOT EXISTS fact_sales
(
    studio_id UInt32,
    date Date,
    portal Nullable(String),
    sku_id Nullable(String),
    gross_revenue Nullable(Float64),
    net_revenue Nullable(Float64),
    units_sold Nullable(UInt32),
    created_at Nullable(DateTime)
)
ENGINE = File(Parquet, '/data/delta/fact_sales/*/*.parquet');

-- Create observation_discounts table
CREATE TABLE IF NOT EXISTS observation_discounts
(
    studio_id UInt32,
    observation_date Date,
    portal Nullable(String),
    sku_id Nullable(String),
    discount_observed Nullable(Float64),
    created_at Nullable(DateTime)
)
ENGINE = File(Parquet, '/data/delta/observation_discounts/*/*.parquet');

-- Create observation_sales table
CREATE TABLE IF NOT EXISTS observation_sales
(
    studio_id UInt32,
    observation_date Date,
    portal Nullable(String),
    sku_id Nullable(String),
    sales_observed Nullable(Float64),
    units_observed Nullable(UInt32),
    created_at Nullable(DateTime)
)
ENGINE = File(Parquet, '/data/delta/observation_sales/*/*.parquet');

-- Create silver_acquisition_properties table
CREATE TABLE IF NOT EXISTS silver_acquisition_properties
(
    studio_id UInt32,
    property_name Nullable(String),
    property_value Nullable(String),
    effective_date Nullable(Date),
    created_at Nullable(DateTime)
)
ENGINE = File(Parquet, '/data/delta/silver_acquisition_properties/*/*.parquet');

-- Create silver_portals table
CREATE TABLE IF NOT EXISTS silver_portals
(
    studio_id UInt32,
    portal_id Nullable(String),
    portal_name Nullable(String),
    portal_type Nullable(String),
    commission_rate Nullable(Float64),
    created_at Nullable(DateTime)
)
ENGINE = File(Parquet, '/data/delta/silver_portals/*/*.parquet');

-- Create silver_reports table
CREATE TABLE IF NOT EXISTS silver_reports
(
    studio_id UInt32,
    report_id Nullable(String),
    report_name Nullable(String),
    report_type Nullable(String),
    report_date Date,
    created_at Nullable(DateTime)
)
ENGINE = File(Parquet, '/data/delta/silver_reports/*/*.parquet');

-- Create silver_skus table
CREATE TABLE IF NOT EXISTS silver_skus
(
    studio_id UInt32,
    sku_id Nullable(String),
    sku_name Nullable(String),
    product_category Nullable(String),
    base_price Nullable(Float64),
    created_at Nullable(DateTime)
)
ENGINE = File(Parquet, '/data/delta/silver_skus/*/*.parquet');
