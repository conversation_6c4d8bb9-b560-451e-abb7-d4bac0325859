-- Create analytics database
CREATE DATABASE IF NOT EXISTS analytics;

-- Use analytics database
USE analytics;

-- Create dim_portals table - auto-detect schema from Parquet files
CREATE TABLE IF NOT EXISTS dim_portals
ENGINE = File(Parquet, '/data/delta/dim_portals/**/*.parquet');

-- Create dim_sku table - auto-detect schema from Parquet files
CREATE TABLE IF NOT EXISTS dim_sku
ENGINE = File(Parquet, '/data/delta/dim_sku/**/*.parquet');

-- Create fact_baseline table - auto-detect schema from Parquet files
CREATE TABLE IF NOT EXISTS fact_baseline
ENGINE = File(Parquet, '/data/delta/fact_baseline/**/*.parquet');

-- Create fact_discounts table - auto-detect schema from Parquet files
CREATE TABLE IF NOT EXISTS fact_discounts
ENGINE = File(Parquet, '/data/delta/fact_discounts/**/*.parquet');

-- Create fact_sales table - auto-detect schema from Parquet files
CREATE TABLE IF NOT EXISTS fact_sales
ENGINE = File(Parquet, '/data/delta/fact_sales/**/*.parquet');

-- Create observation_discounts table - auto-detect schema from Parquet files
CREATE TABLE IF NOT EXISTS observation_discounts
ENGINE = File(Parquet, '/data/delta/observation_discounts/**/*.parquet');

-- Create observation_sales table - auto-detect schema from Parquet files
CREATE TABLE IF NOT EXISTS observation_sales
ENGINE = File(Parquet, '/data/delta/observation_sales/**/*.parquet');

-- Create silver_acquisition_properties table - auto-detect schema from Parquet files
CREATE TABLE IF NOT EXISTS silver_acquisition_properties
ENGINE = File(Parquet, '/data/delta/silver_acquisition_properties/**/*.parquet');

-- Create silver_portals table - auto-detect schema from Parquet files
CREATE TABLE IF NOT EXISTS silver_portals
ENGINE = File(Parquet, '/data/delta/silver_portals/**/*.parquet');

-- Create silver_reports table - auto-detect schema from Parquet files
CREATE TABLE IF NOT EXISTS silver_reports
ENGINE = File(Parquet, '/data/delta/silver_reports/**/*.parquet');

-- Create silver_skus table - auto-detect schema from Parquet files
CREATE TABLE IF NOT EXISTS silver_skus
ENGINE = File(Parquet, '/data/delta/silver_skus/**/*.parquet');
