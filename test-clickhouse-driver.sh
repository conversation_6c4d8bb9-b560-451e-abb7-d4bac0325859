#!/bin/bash

echo "🧪 Testing ClickHouse Driver in Superset..."
echo "============================================"

# Test 1: Check if ClickHouse packages are installed
echo "🔍 Testing ClickHouse package installation..."
packages=$(docker exec superset pip list | grep -i clickhouse)
if [[ $packages == *"clickhouse-sqlalchemy"* ]]; then
    echo "✅ ClickHouse SQLAlchemy package installed"
    echo "   $packages"
else
    echo "❌ ClickHouse SQLAlchemy package missing"
    exit 1
fi

# Test 2: Test Python import
echo "🔍 Testing ClickHouse SQLAlchemy import..."
import_test=$(docker exec superset python -c "import clickhouse_sqlalchemy; print('SUCCESS')" 2>&1)
if [[ $import_test == "SUCCESS" ]]; then
    echo "✅ ClickHouse SQLAlchemy import successful"
else
    echo "❌ ClickHouse SQLAlchemy import failed: $import_test"
    exit 1
fi

# Test 3: Test SQLAlchemy dialect registration
echo "🔍 Testing ClickHouse dialect registration..."
dialect_test=$(docker exec superset python -c "
from sqlalchemy import create_engine
try:
    engine = create_engine('clickhouse+http://clickhouse:8123/analytics')
    print('SUCCESS')
except Exception as e:
    print(f'ERROR: {e}')
" 2>&1)

if [[ $dialect_test == "SUCCESS" ]]; then
    echo "✅ ClickHouse dialect registration successful"
else
    echo "❌ ClickHouse dialect registration failed: $dialect_test"
    exit 1
fi

# Test 4: Test actual connection
echo "🔍 Testing actual ClickHouse connection..."
connection_test=$(docker exec superset python -c "
from sqlalchemy import create_engine, text
try:
    engine = create_engine('clickhouse+http://clickhouse:8123/analytics')
    with engine.connect() as conn:
        result = conn.execute(text('SELECT COUNT(*) FROM dim_portals'))
        count = result.scalar()
        print(f'SUCCESS: {count}')
except Exception as e:
    print(f'ERROR: {e}')
" 2>&1)

if [[ $connection_test == "SUCCESS: 19" ]]; then
    echo "✅ ClickHouse connection test successful"
    echo "   Query result: 19 rows in dim_portals"
else
    echo "❌ ClickHouse connection test failed: $connection_test"
    exit 1
fi

echo ""
echo "🎉 All ClickHouse driver tests passed!"
echo ""
echo "📋 **Ready to Connect in Superset:**"
echo "1. Go to http://localhost:8088"
echo "2. Login with admin/admin"
echo "3. Go to Settings → Database Connections"
echo "4. Click '+ Database'"
echo "5. Select 'ClickHouse' as database type"
echo "6. Use this connection string:"
echo "   clickhouse+http://clickhouse:8123/analytics"
echo "7. Click 'Test Connection' - it should now work!"
echo ""
echo "✅ **Driver Status:**"
echo "   ClickHouse SQLAlchemy: v0.2.4 (compatible)"
echo "   SQLAlchemy: v1.4.x (compatible)"
echo "   Connection: HTTP protocol on port 8123"
