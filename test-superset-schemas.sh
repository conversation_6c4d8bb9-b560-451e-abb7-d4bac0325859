#!/bin/bash

echo "🔍 Testing Superset Schema Detection..."
echo "====================================="

echo "📋 Available databases in ClickHouse:"
curl -s "http://localhost:8123/?query=SELECT%20name%20FROM%20system.databases%20ORDER%20BY%20name"
echo ""

echo "📋 Tables in analytics database:"
curl -s "http://localhost:8123/?query=SELECT%20name%20FROM%20system.tables%20WHERE%20database%20=%20'analytics'%20ORDER%20BY%20name"
echo ""

echo "📋 Tables in default database:"
curl -s "http://localhost:8123/?query=SELECT%20name%20FROM%20system.tables%20WHERE%20database%20=%20'default'%20ORDER%20BY%20name"
echo ""

echo "🔧 **Troubleshooting Steps for Superset:**"
echo ""
echo "1. **Delete your current ClickHouse connection in Superset**"
echo ""
echo "2. **Create a new connection with one of these URIs:**"
echo "   Option A (recommended): clickhouse+http://clickhouse:8123/"
echo "   Option B: clickhouse+http://clickhouse:8123/default"
echo ""
echo "3. **After connecting successfully:**"
echo "   - Click 'Force refresh schema list'"
echo "   - You should see 'analytics' and 'default' as available schemas"
echo "   - Select 'analytics' schema to see your Delta Lake tables"
echo ""
echo "4. **If you still don't see schemas:**"
echo "   - Try refreshing the page"
echo "   - Check the browser console for errors"
echo "   - Try connecting to the specific database: clickhouse+http://clickhouse:8123/analytics"
echo ""
echo "📊 **Expected Result:**"
echo "   Schema: analytics"
echo "   Tables: dim_portals, dim_sku, fact_baseline, etc."
echo ""
echo "🔗 **Alternative Connection Strings to Try:**"
echo "   clickhouse+http://clickhouse:8123/"
echo "   clickhouse+http://clickhouse:8123/default"
echo "   clickhouse+http://clickhouse:8123/analytics"
