#!/bin/bash

echo "🔄 Updating ClickHouse tables to use DeltaLake engine..."
echo "======================================================"

# Function to check if <PERSON>lick<PERSON>ouse is running
check_clickhouse() {
    if ! docker exec clickhouse-server clickhouse-client --query "SELECT 1" > /dev/null 2>&1; then
        echo "❌ ClickHouse is not running. Please start it first with:"
        echo "   docker-compose up -d clickhouse"
        exit 1
    fi
    echo "✅ ClickHouse is running"
}

# Function to drop existing tables
drop_existing_tables() {
    echo "🗑️  Dropping existing File engine tables..."
    
    tables=("dim_portals" "dim_sku" "fact_baseline" "fact_discounts" "fact_sales" "observation_discounts" "observation_sales" "silver_acquisition_properties" "silver_portals" "silver_reports" "silver_skus")
    
    for table in "${tables[@]}"; do
        echo "   Dropping $table..."
        docker exec clickhouse-server clickhouse-client --query "DROP TABLE IF EXISTS analytics.${table}" 2>/dev/null
    done
    
    echo "✅ Old tables dropped"
}

# Function to create tables with improved File engine
create_improved_tables() {
    echo "🏗️  Creating tables with improved File engine (auto-detect schema)..."

    # Create database first
    docker exec clickhouse-server clickhouse-client --query "CREATE DATABASE IF NOT EXISTS analytics"

    # List of tables to create
    tables=("dim_portals" "dim_sku" "fact_baseline" "fact_discounts" "fact_sales" "observation_discounts" "observation_sales" "silver_acquisition_properties" "silver_portals" "silver_reports" "silver_skus")

    for table in "${tables[@]}"; do
        echo "   Creating $table..."

        # Create table with auto-detected schema using ** pattern for recursive search
        docker exec clickhouse-server clickhouse-client --query "
            CREATE TABLE IF NOT EXISTS analytics.${table}
            ENGINE = File(Parquet, '/data/delta/${table}/**/*.parquet')
        " 2>/dev/null

        if [ $? -eq 0 ]; then
            echo "   ✅ $table created successfully"
        else
            echo "   ❌ Failed to create $table, trying alternative pattern..."
            # Try with single level pattern if recursive doesn't work
            docker exec clickhouse-server clickhouse-client --query "
                CREATE TABLE IF NOT EXISTS analytics.${table}
                ENGINE = File(Parquet, '/data/delta/${table}/*/*.parquet')
            " 2>/dev/null

            if [ $? -eq 0 ]; then
                echo "   ✅ $table created with single-level pattern"
            else
                echo "   ❌ Failed to create $table with both patterns"
            fi
        fi
    done

    echo "✅ Table creation completed"
}

# Function to test tables
test_tables() {
    echo "🧪 Testing DeltaLake tables..."
    
    tables=("dim_portals" "dim_sku" "fact_baseline" "fact_discounts" "fact_sales" "observation_discounts" "observation_sales" "silver_acquisition_properties" "silver_portals" "silver_reports" "silver_skus")
    
    for table in "${tables[@]}"; do
        echo -n "   Testing $table... "
        
        # Try to get row count and first few column names
        result=$(docker exec clickhouse-server clickhouse-client --query "
            SELECT 
                COUNT(*) as row_count,
                groupArray(name) as columns
            FROM (
                SELECT name 
                FROM system.columns 
                WHERE database = 'analytics' AND table = '$table' 
                LIMIT 5
            )
        " 2>/dev/null)
        
        if [ $? -eq 0 ]; then
            echo "✅ $result"
        else
            echo "❌ Failed"
        fi
    done
}

# Function to show table schemas
show_schemas() {
    echo "📋 Table schemas:"
    echo "=================="
    
    docker exec clickhouse-server clickhouse-client --query "
        SELECT 
            table,
            name as column_name,
            type as column_type
        FROM system.columns 
        WHERE database = 'analytics' 
        ORDER BY table, position
    " --format PrettyCompact
}

# Function to test a specific table with sample data
test_fact_sales() {
    echo "🎯 Testing fact_sales table with sample query..."
    echo "================================================"
    
    docker exec clickhouse-server clickhouse-client --query "
        SELECT 
            COUNT(*) as total_rows,
            COUNT(DISTINCT date) as unique_dates,
            MIN(date) as earliest_date,
            MAX(date) as latest_date
        FROM analytics.fact_sales
        LIMIT 1
    " --format PrettyCompact 2>/dev/null
    
    if [ $? -eq 0 ]; then
        echo ""
        echo "📊 Sample data from fact_sales:"
        docker exec clickhouse-server clickhouse-client --query "
            SELECT *
            FROM analytics.fact_sales
            LIMIT 3
        " --format PrettyCompact 2>/dev/null
    else
        echo "❌ Could not query fact_sales table"
    fi
}

# Main execution
echo "🚀 Starting DeltaLake migration..."
echo ""

check_clickhouse
echo ""

drop_existing_tables
echo ""

create_improved_tables
echo ""

test_tables
echo ""

show_schemas
echo ""

test_fact_sales
echo ""

echo "🎉 DeltaLake migration completed!"
echo ""
echo "✅ **Benefits of improved File engine:**"
echo "   - Automatic schema detection from Parquet files"
echo "   - Recursive search pattern (**/*.parquet) finds all files"
echo "   - Better compatibility with Delta Lake directory structure"
echo "   - No more File engine path pattern issues"
echo ""
echo "🔄 **Next steps:**"
echo "1. Refresh your Superset connection"
echo "2. Go to Data -> Datasets and sync the database schema"
echo "3. Create new datasets - they should now show correct columns"
echo "4. The tables will automatically reflect the actual Delta Lake schema"
