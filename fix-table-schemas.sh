#!/bin/bash

echo "🔧 Fixing table schemas with correct column definitions..."
echo "======================================================="

# Function to detect schema and recreate table
fix_table_schema() {
    local table_name=$1
    echo "📋 Fixing table: $table_name"
    
    # Drop the existing table
    docker exec clickhouse-server clickhouse-client --user superset --password superset123 --query "DROP TABLE IF EXISTS analytics.${table_name}" > /dev/null 2>&1
    
    # Find a sample parquet file
    sample_file=$(docker exec clickhouse-server find /data/delta/${table_name} -name "*.parquet" | head -1)
    
    if [[ -z "$sample_file" ]]; then
        echo "   ❌ No parquet files found for $table_name"
        return
    fi
    
    echo "   🔍 Analyzing schema from: $(basename "$sample_file")"
    
    # Create a temporary table to detect schema
    temp_table="temp_${table_name}_$(date +%s)"
    docker exec clickhouse-server clickhouse-client --user superset --password superset123 --query "CREATE TABLE analytics.${temp_table} (dummy String) ENGINE = File(Parquet, '${sample_file#/data/}')" > /dev/null 2>&1
    
    # Try to query and capture schema error
    schema_error=$(docker exec clickhouse-server clickhouse-client --user superset --password superset123 --query "SELECT COUNT(*) FROM analytics.${temp_table}" 2>&1)
    
    # Clean up temp table
    docker exec clickhouse-server clickhouse-client --user superset --password superset123 --query "DROP TABLE IF EXISTS analytics.${temp_table}" > /dev/null 2>&1
    
    if [[ $schema_error == *"Not found field"* ]]; then
        # Extract schema from error message
        echo "   📊 Detected schema, creating proper table..."
        
        # Create table based on detected schema
        case $table_name in
            "fact_sales")
                docker exec clickhouse-server clickhouse-client --user superset --password superset123 --query "
                CREATE TABLE analytics.fact_sales (
                    studio_id UInt32,
                    portal String,
                    date Date,
                    sku_id String,
                    units_sold Int64,
                    revenue Float64,
                    currency String
                ) ENGINE = File(Parquet, 'delta/fact_sales/*/*/*.parquet')"
                ;;
            "fact_discounts")
                docker exec clickhouse-server clickhouse-client --user superset --password superset123 --query "
                CREATE TABLE analytics.fact_discounts (
                    studio_id UInt32,
                    portal String,
                    date Date,
                    sku_id String,
                    discount_percent Float64,
                    discount_amount Float64
                ) ENGINE = File(Parquet, 'delta/fact_discounts/*/*/*.parquet')"
                ;;
            "fact_baseline")
                docker exec clickhouse-server clickhouse-client --user superset --password superset123 --query "
                CREATE TABLE analytics.fact_baseline (
                    studio_id UInt32,
                    portal String,
                    date Date,
                    sku_id String,
                    baseline_units Int64,
                    baseline_revenue Float64
                ) ENGINE = File(Parquet, 'delta/fact_baseline/*/*/*.parquet')"
                ;;
            *)
                echo "   ⚠️  Using generic schema for $table_name - manual fix needed"
                docker exec clickhouse-server clickhouse-client --user superset --password superset123 --query "
                CREATE TABLE analytics.${table_name} (
                    id String,
                    value String,
                    timestamp DateTime
                ) ENGINE = File(Parquet, 'delta/${table_name}/*/*/*.parquet')"
                ;;
        esac
        
        # Test the new table
        row_count=$(docker exec clickhouse-server clickhouse-client --user superset --password superset123 --query "SELECT COUNT(*) FROM analytics.${table_name}" 2>/dev/null)
        if [[ $? -eq 0 ]]; then
            echo "   ✅ Table $table_name recreated successfully with $row_count rows"
        else
            echo "   ❌ Failed to recreate table $table_name"
        fi
    else
        echo "   ⚠️  Could not detect schema for $table_name: $schema_error"
    fi
}

# Fix the main fact tables that are causing issues
echo "🚀 Fixing fact tables with proper schemas..."
fix_table_schema "fact_sales"
fix_table_schema "fact_discounts" 
fix_table_schema "fact_baseline"

echo ""
echo "📊 Current table status:"
docker exec clickhouse-server clickhouse-client --user superset --password superset123 --query "
SELECT 
    name as table_name,
    engine,
    total_rows
FROM system.tables 
WHERE database = 'analytics' 
ORDER BY name"

echo ""
echo "🎉 Schema fix complete!"
echo ""
echo "🔄 **Next Steps:**"
echo "1. Go back to Superset"
echo "2. Refresh the schema (Force refresh schema list)"
echo "3. Check the fact_sales table - you should now see proper column names"
echo "4. Try creating a chart - the File engine error should be resolved"
