#!/bin/bash

# Test script for Analytics Stack
# This script tests the ClickHouse and Superset setup

set -e

echo "🧪 Testing Analytics Stack Setup..."
echo "==================================="

# Function to test ClickHouse connection
test_clickhouse() {
    echo "🔍 Testing ClickHouse connection..."
    
    # Test basic connection
    if curl -s http://localhost:8123/ping | grep -q "Ok"; then
        echo "✅ ClickHouse HTTP interface is responding"
    else
        echo "❌ ClickHouse HTTP interface is not responding"
        return 1
    fi
    
    # Test database connection
    if docker exec clickhouse-server clickhouse-client --query "SELECT 1" > /dev/null 2>&1; then
        echo "✅ ClickHouse client connection successful"
    else
        echo "❌ ClickHouse client connection failed"
        return 1
    fi
    
    # Test analytics database
    if docker exec clickhouse-server clickhouse-client --query "SHOW DATABASES" | grep -q "analytics"; then
        echo "✅ Analytics database exists"
    else
        echo "❌ Analytics database not found"
        return 1
    fi
    
    # Test tables
    echo "📋 Checking tables in analytics database..."
    tables=$(docker exec clickhouse-server clickhouse-client --query "SHOW TABLES FROM analytics")
    if [ -n "$tables" ]; then
        echo "✅ Tables found in analytics database:"
        echo "$tables" | sed 's/^/   - /'
    else
        echo "⚠️  No tables found in analytics database"
    fi
    
    # Test sample query on a table (if it exists)
    if echo "$tables" | grep -q "dim_portals"; then
        echo "🔍 Testing sample query on dim_portals..."
        count=$(docker exec clickhouse-server clickhouse-client --query "SELECT COUNT(*) FROM analytics.dim_portals" 2>/dev/null || echo "0")
        echo "✅ dim_portals table has $count rows"
    fi
}

# Function to test Superset connection
test_superset() {
    echo "🔍 Testing Superset connection..."
    
    # Test Superset health endpoint
    if curl -s http://localhost:8088/health > /dev/null 2>&1; then
        echo "✅ Superset is responding"
    else
        echo "❌ Superset is not responding"
        return 1
    fi
    
    # Test Superset login page
    if curl -s http://localhost:8088/login/ | grep -q "Superset"; then
        echo "✅ Superset login page is accessible"
    else
        echo "❌ Superset login page is not accessible"
        return 1
    fi
}

# Function to test Docker services
test_docker_services() {
    echo "🔍 Testing Docker services..."
    
    # Check if containers are running
    if docker ps | grep -q "clickhouse-server"; then
        echo "✅ ClickHouse container is running"
    else
        echo "❌ ClickHouse container is not running"
        return 1
    fi
    
    if docker ps | grep -q "superset"; then
        echo "✅ Superset container is running"
    else
        echo "❌ Superset container is not running"
        return 1
    fi
    
    # Check container health
    clickhouse_health=$(docker inspect --format='{{.State.Health.Status}}' clickhouse-server 2>/dev/null || echo "unknown")
    echo "📊 ClickHouse health status: $clickhouse_health"
}

# Function to test Delta Lake data access
test_delta_lake_access() {
    echo "🔍 Testing Delta Lake data access..."
    
    # Check if Delta Lake directory is accessible from ClickHouse container
    if docker exec clickhouse-server ls /data/delta > /dev/null 2>&1; then
        echo "✅ Delta Lake directory is accessible from ClickHouse"
        
        # List available tables
        tables=$(docker exec clickhouse-server ls /data/delta)
        echo "📁 Available Delta Lake tables:"
        echo "$tables" | sed 's/^/   - /'
        
        # Check if parquet files exist
        parquet_count=$(docker exec clickhouse-server find /data/delta -name "*.parquet" | wc -l)
        echo "📄 Found $parquet_count Parquet files"
    else
        echo "❌ Delta Lake directory is not accessible from ClickHouse"
        return 1
    fi
}

# Main test execution
main() {
    echo "Starting comprehensive tests..."
    echo ""
    
    # Check if Docker is running
    if ! docker info > /dev/null 2>&1; then
        echo "❌ Docker is not running. Please start Docker first."
        exit 1
    fi
    
    # Check if services are running
    if ! docker ps | grep -q "clickhouse-server\|superset"; then
        echo "❌ Analytics stack is not running. Please run ./start-analytics-stack.sh first."
        exit 1
    fi
    
    # Run tests
    test_docker_services
    echo ""
    
    test_delta_lake_access
    echo ""
    
    test_clickhouse
    echo ""
    
    test_superset
    echo ""
    
    echo "🎉 All tests completed!"
    echo ""
    echo "📋 Summary:"
    echo "  - ClickHouse: http://localhost:8123"
    echo "  - Superset: http://localhost:8088"
    echo "  - Default credentials in README.md"
    echo ""
    echo "🔗 To connect Superset to ClickHouse:"
    echo "  SQLAlchemy URI: clickhouse+native://admin:admin123@clickhouse:9000/analytics"
}

# Run main function
main
